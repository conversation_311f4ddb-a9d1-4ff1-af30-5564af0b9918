\documentclass[tikz,border=10pt]{standalone}
\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}
\usepackage{CJKutf8}
\usepackage{tikz}
\usetikzlibrary{shapes,arrows,positioning,fit,backgrounds}

\begin{document}
\begin{CJK}{UTF8}{gbsn}

% 定义样式
\tikzstyle{startstop} = [rectangle, rounded corners, minimum width=3cm, minimum height=1cm, text centered, draw=black, fill=red!30]
\tikzstyle{process} = [rectangle, minimum width=3cm, minimum height=1cm, text centered, draw=black, fill=orange!30]
\tikzstyle{decision} = [diamond, minimum width=3cm, minimum height=1cm, text centered, draw=black, fill=green!30]
\tikzstyle{io} = [trapezium, trapezium left angle=70, trapezium right angle=110, minimum width=3cm, minimum height=1cm, text centered, draw=black, fill=blue!30]
\tikzstyle{arrow} = [thick,->,>=stealth]

\begin{tikzpicture}[node distance=2cm]

% Start node
\node (start) [startstop] {Start};

% Environment initialization
\node (init) [process, below of=start] {Environment Init\\Set start, goal, obstacles};

% State observation
\node (observe) [io, below of=init] {Get Current State\\$\mathbf{s}_t = [x,y,z,v_x,v_y,v_z]$};

% DWA safe action generation
\node (dwa) [process, below of=observe] {DWA Safe Action\\Generation};

% DWA detailed steps
\node (dw_calc) [process, left of=dwa, xshift=-5cm] {Calculate Dynamic Window\\$DW = [v_{min}, v_{max}]$};
\node (traj_pred) [process, below of=dw_calc] {Trajectory Prediction\\$\tau = \{p_1, p_2, ..., p_n\}$};
\node (safety_check) [decision, below of=traj_pred] {Safety Check\\$d_{obs} \geq d_{safe}$?};
\node (action_eval) [process, below of=safety_check] {Action Evaluation\\Score = f(heading,\\velocity, distance)};

% TD3 decision
\node (td3) [process, below of=dwa] {TD3 Intelligent\\Decision};

% TD3 detailed steps
\node (state_encode) [process, right of=td3, xshift=5cm] {State Encoding\\$\mathbf{h}_{state} = Encoder(\mathbf{s})$};
\node (action_encode) [process, below of=state_encode] {Action Encoding\\$\mathbf{h}_{action} = Encoder(\mathcal{A}_{safe})$};
\node (attention) [process, below of=action_encode] {Attention Mechanism\\$\mathbf{h} = Attention(\mathbf{h}_{state},\\\mathbf{h}_{action})$};
\node (action_select) [process, below of=attention] {Action Selection\\$\mathbf{a} = \arg\max Q(\mathbf{s}, \mathbf{a})$};

% Action execution
\node (execute) [process, below of=td3, yshift=-2cm] {Execute Selected Action\\$\mathbf{s}_{t+1} = f(\mathbf{s}_t, \mathbf{a}_t)$};

% Reward calculation
\node (reward) [process, below of=execute] {Calculate Reward\\$R_t = R_{goal} + R_{safety} + ...$};

% Experience storage
\node (store) [process, below of=reward] {Store Experience\\$(\mathbf{s}_t, \mathbf{a}_t, R_t, \mathbf{s}_{t+1})$};

% Network training
\node (train) [decision, below of=store] {Train\\Network?};

% Training steps
\node (sample) [process, left of=train, xshift=-4cm] {Sample Batch\\$Batch \sim Buffer$};
\node (critic_update) [process, below of=sample] {Update Critic\\$\min L_{critic}$};
\node (actor_update) [process, below of=critic_update] {Update Actor\\$\max J_{actor}$};
\node (target_update) [process, below of=actor_update] {Soft Update Target Networks\\$\theta' \leftarrow \tau\theta + (1-\tau)\theta'$};

% Termination condition check
\node (check_done) [decision, below of=train, yshift=-2cm] {Reach Goal\\or Collision?};

% End
\node (end) [startstop, below of=check_done] {End};

% Connections
\draw [arrow] (start) -- (init);
\draw [arrow] (init) -- (observe);
\draw [arrow] (observe) -- (dwa);

% DWA flow
\draw [arrow] (dwa) -- (dw_calc);
\draw [arrow] (dw_calc) -- (traj_pred);
\draw [arrow] (traj_pred) -- (safety_check);
\draw [arrow] (safety_check) -- node[anchor=east] {Yes} (action_eval);
\draw [arrow] (safety_check.east) -- ++(2,0) |- node[anchor=south] {No} (traj_pred.east);
\draw [arrow] (action_eval.east) -- ++(3,0) |- (dwa.south);

% TD3 flow
\draw [arrow] (dwa) -- (td3);
\draw [arrow] (td3) -- (state_encode);
\draw [arrow] (state_encode) -- (action_encode);
\draw [arrow] (action_encode) -- (attention);
\draw [arrow] (attention) -- (action_select);
\draw [arrow] (action_select.west) -- ++(-3,0) |- (td3.south);

\draw [arrow] (td3) -- (execute);
\draw [arrow] (execute) -- (reward);
\draw [arrow] (reward) -- (store);
\draw [arrow] (store) -- (train);

% Training branch
\draw [arrow] (train) -- node[anchor=east] {Yes} (sample);
\draw [arrow] (sample) -- (critic_update);
\draw [arrow] (critic_update) -- (actor_update);
\draw [arrow] (actor_update) -- (target_update);
\draw [arrow] (target_update.east) -- ++(2,0) |- (check_done.west);

% No training branch
\draw [arrow] (train) -- node[anchor=west] {No} (check_done);

% Termination check
\draw [arrow] (check_done) -- node[anchor=east] {Yes} (end);
\draw [arrow] (check_done.west) -- ++(-6,0) |- node[anchor=south] {No} (observe.west);

% Add background boxes
\begin{scope}[on background layer]
\node[fit=(dw_calc)(action_eval), fill=yellow!20, draw=black, dashed, inner sep=10pt, label=above:DWA Safety Constraint Layer] {};
\node[fit=(state_encode)(action_select), fill=cyan!20, draw=black, dashed, inner sep=10pt, label=above:TD3 Intelligent Decision Layer] {};
\node[fit=(sample)(target_update), fill=purple!20, draw=black, dashed, inner sep=10pt, label=above:Network Training Module] {};
\end{scope}

\end{tikzpicture}

\end{CJK}
\end{document}
