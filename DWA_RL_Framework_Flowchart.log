This is pdfTeX, Version 3.141592653-2.6-1.40.26 (TeX Live 2024) (preloaded format=pdflatex 2024.11.28)  12 JUL 2025 20:37
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**e:/basic_dwa_rl_framework/DWA_RL_Framework_Flowchart.tex
(e:/basic_dwa_rl_framework/DWA_RL_Framework_Flowchart.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
(e:/texlive/2024/texmf-dist/tex/latex/standalone/standalone.cls
Document Class: standalone 2022/10/10 v1.3b Class to compile TeX sub-files standalone
(e:/texlive/2024/texmf-dist/tex/latex/tools/shellesc.sty
Package: shellesc 2023/07/08 v1.0d unified shell escape interface for LaTeX
Package shellesc Info: Restricted shell escape enabled on input line 77.
) (e:/texlive/2024/texmf-dist/tex/generic/iftex/ifluatex.sty
Package: ifluatex 2019/10/25 v1.5 ifluatex legacy package. Use iftex instead.
 (e:/texlive/2024/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
)) (e:/texlive/2024/texmf-dist/tex/latex/xkeyval/xkeyval.sty
Package: xkeyval 2022/06/16 v2.9 package option processing (HA)
 (e:/texlive/2024/texmf-dist/tex/generic/xkeyval/xkeyval.tex (e:/texlive/2024/texmf-dist/tex/generic/xkeyval/xkvutils.tex
\XKV@toks=\toks17
\XKV@tempa@toks=\toks18
 (e:/texlive/2024/texmf-dist/tex/generic/xkeyval/keyval.tex))
\XKV@depth=\count188
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
))
\sa@internal=\count189
\c@sapage=\count190
 (e:/texlive/2024/texmf-dist/tex/latex/standalone/standalone.cfg
File: standalone.cfg 2022/10/10 v1.3b Default configuration file for 'standalone' class
) (e:/texlive/2024/texmf-dist/tex/latex/base/article.cls
Document Class: article 2023/05/17 v1.4n Standard LaTeX document class
(e:/texlive/2024/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
)
\c@part=\count191
\c@section=\count192
\c@subsection=\count193
\c@subsubsection=\count194
\c@paragraph=\count195
\c@subparagraph=\count196
\c@figure=\count197
\c@table=\count198
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\bibindent=\dimen140
) (e:/texlive/2024/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty (e:/texlive/2024/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty (e:/texlive/2024/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty (e:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks19
\pgfutil@tempdima=\dimen141
\pgfutil@tempdimb=\dimen142
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box51
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex (e:/texlive/2024/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)
 (e:/texlive/2024/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty (e:/texlive/2024/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (e:/texlive/2024/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)
 (e:/texlive/2024/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
) (e:/texlive/2024/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 107.
 (e:/texlive/2024/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2022/09/22 v1.2b Graphics/color driver for pdftex
))
\Gin@req@height=\dimen143
\Gin@req@width=\dimen144
) (e:/texlive/2024/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty (e:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)
 (e:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks20
\pgfkeys@temptoks=\toks21
 (e:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfkeyslibraryfiltered.code.tex
\pgfkeys@tmptoks=\toks22
))
\pgf@x=\dimen145
\pgf@y=\dimen146
\pgf@xa=\dimen147
\pgf@ya=\dimen148
\pgf@xb=\dimen149
\pgf@yb=\dimen150
\pgf@xc=\dimen151
\pgf@yc=\dimen152
\pgf@xd=\dimen153
\pgf@yd=\dimen154
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count199
\c@pgf@countb=\count266
\c@pgf@countc=\count267
\c@pgf@countd=\count268
\t@pgf@toka=\toks23
\t@pgf@tokb=\toks24
\t@pgf@tokc=\toks25
\pgf@sys@id@count=\count269
 (e:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-pdftex.def
 (e:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-pdftex.def
File: pgfsys-pdftex.def 2023-01-15 v3.1.10 (3.1.10)
 (e:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
))) (e:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count270
\pgfsyssoftpath@bigbuffer@items=\count271
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (e:/texlive/2024/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)
 (e:/texlive/2024/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.
 (e:/texlive/2024/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1354.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)
 (e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex (e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen155
\pgfmath@count=\count272
\pgfmath@box=\box52
\pgfmath@toks=\toks26
\pgfmath@stack@operand=\toks27
\pgfmath@stack@operation=\toks28
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigonometric.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.random.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.comparison.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integerarithmetics.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count273
)) (e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfint.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen156
\pgf@picmaxx=\dimen157
\pgf@picminy=\dimen158
\pgf@picmaxy=\dimen159
\pgf@pathminx=\dimen160
\pgf@pathmaxx=\dimen161
\pgf@pathminy=\dimen162
\pgf@pathmaxy=\dimen163
\pgf@xx=\dimen164
\pgf@xy=\dimen165
\pgf@yx=\dimen166
\pgf@yy=\dimen167
\pgf@zx=\dimen168
\pgf@zy=\dimen169
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen170
\pgf@path@lasty=\dimen171
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen172
\pgf@shorten@start@additional=\dimen173
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box53
\pgf@hbox=\box54
\pgf@layerbox@main=\box55
\pgf@picture@serial@count=\count274
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen174
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransformations.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen175
\pgf@pt@y=\dimen176
\pgf@pt@temp=\dimen177
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen178
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen179
\pgf@sys@shading@range@num=\count275
\pgf@shadingcount=\count276
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box56
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (e:/texlive/2024/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box57
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (e:/texlive/2024/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen180
\pgf@nodesepend=\dimen181
) (e:/texlive/2024/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
)) (e:/texlive/2024/texmf-dist/tex/latex/pgf/utilities/pgffor.sty (e:/texlive/2024/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty (e:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex)) (e:/texlive/2024/texmf-dist/tex/latex/pgf/math/pgfmath.sty (e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex)) (e:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgffor.code.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen182
\pgffor@skip=\dimen183
\pgffor@stack=\toks29
\pgffor@toks=\toks30
)) (e:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)
 (e:/texlive/2024/texmf-dist/tex/generic/pgf/libraries/pgflibraryplothandlers.code.tex
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count277
\pgfplotmarksize=\dimen184
)
\tikz@lastx=\dimen185
\tikz@lasty=\dimen186
\tikz@lastxsaved=\dimen187
\tikz@lastysaved=\dimen188
\tikz@lastmovetox=\dimen189
\tikz@lastmovetoy=\dimen190
\tikzleveldistance=\dimen191
\tikzsiblingdistance=\dimen192
\tikz@figbox=\box58
\tikz@figbox@bg=\box59
\tikz@tempbox=\box60
\tikz@tempbox@bg=\box61
\tikztreelevel=\count278
\tikznumberofchildren=\count279
\tikznumberofcurrentchild=\count280
\tikz@fig@count=\count281
 (e:/texlive/2024/texmf-dist/tex/generic/pgf/modules/pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count282
\pgfmatrixcurrentcolumn=\count283
\pgf@matrix@numberofcolumns=\count284
)
\tikz@expandcount=\count285
 (e:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
\sa@box=\box62
) (e:/texlive/2024/texmf-dist/tex/latex/base/inputenc.sty
Package: inputenc 2021/02/14 v1.3d Input encoding file
\inpenc@prehook=\toks31
\inpenc@posthook=\toks32
) (e:/texlive/2024/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
) (e:/texlive/2024/texmf-dist/tex/latex/cjk/texinput/CJKutf8.sty
Package: CJKutf8 2021/10/16 4.8.5
 (e:/texlive/2024/texmf-dist/tex/generic/iftex/ifpdf.sty
Package: ifpdf 2019/10/25 v3.4 ifpdf legacy package. Use iftex instead.
) (e:/texlive/2024/texmf-dist/tex/latex/cjk/texinput/CJK.sty
Package: CJK 2021/10/16 4.8.5
 (e:/texlive/2024/texmf-dist/tex/latex/cjk/texinput/mule/MULEenc.sty
Package: MULEenc 2021/10/16 4.8.5
) (e:/texlive/2024/texmf-dist/tex/latex/cjk/texinput/CJK.enc
File: CJK.enc 2021/10/16 4.8.5
Now handling font encoding C00 ...
... no UTF-8 mapping file for font encoding C00
Now handling font encoding C05 ...
... no UTF-8 mapping file for font encoding C05
Now handling font encoding C09 ...
... no UTF-8 mapping file for font encoding C09
Now handling font encoding C10 ...
... no UTF-8 mapping file for font encoding C10
Now handling font encoding C20 ...
... no UTF-8 mapping file for font encoding C20
Now handling font encoding C19 ...
... no UTF-8 mapping file for font encoding C19
Now handling font encoding C40 ...
... no UTF-8 mapping file for font encoding C40
Now handling font encoding C42 ...
... no UTF-8 mapping file for font encoding C42
Now handling font encoding C43 ...
... no UTF-8 mapping file for font encoding C43
Now handling font encoding C50 ...
... no UTF-8 mapping file for font encoding C50
Now handling font encoding C52 ...
... no UTF-8 mapping file for font encoding C52
Now handling font encoding C49 ...
... no UTF-8 mapping file for font encoding C49
Now handling font encoding C60 ...
... no UTF-8 mapping file for font encoding C60
Now handling font encoding C61 ...
... no UTF-8 mapping file for font encoding C61
Now handling font encoding C63 ...
... no UTF-8 mapping file for font encoding C63
Now handling font encoding C64 ...
... no UTF-8 mapping file for font encoding C64
Now handling font encoding C65 ...
... no UTF-8 mapping file for font encoding C65
Now handling font encoding C70 ...
... no UTF-8 mapping file for font encoding C70
Now handling font encoding C31 ...
... no UTF-8 mapping file for font encoding C31
Now handling font encoding C32 ...
... no UTF-8 mapping file for font encoding C32
Now handling font encoding C33 ...
... no UTF-8 mapping file for font encoding C33
Now handling font encoding C34 ...
... no UTF-8 mapping file for font encoding C34
Now handling font encoding C35 ...
... no UTF-8 mapping file for font encoding C35
Now handling font encoding C36 ...
... no UTF-8 mapping file for font encoding C36
Now handling font encoding C37 ...
... no UTF-8 mapping file for font encoding C37
Now handling font encoding C80 ...
... no UTF-8 mapping file for font encoding C80
Now handling font encoding C81 ...
... no UTF-8 mapping file for font encoding C81
Now handling font encoding C01 ...
... no UTF-8 mapping file for font encoding C01
Now handling font encoding C11 ...
... no UTF-8 mapping file for font encoding C11
Now handling font encoding C21 ...
... no UTF-8 mapping file for font encoding C21
Now handling font encoding C41 ...
... no UTF-8 mapping file for font encoding C41
Now handling font encoding C62 ...
... no UTF-8 mapping file for font encoding C62
)
\CJK@indent=\box63
) (e:/texlive/2024/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
)) (e:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibraryshapes.code.tex
File: tikzlibraryshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
 (e:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibraryshapes.geometric.code.tex
File: tikzlibraryshapes.geometric.code.tex 2023-01-15 v3.1.10 (3.1.10)
 (e:/texlive/2024/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshapes.geometric.code.tex
File: pgflibraryshapes.geometric.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (e:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibraryshapes.misc.code.tex
File: tikzlibraryshapes.misc.code.tex 2023-01-15 v3.1.10 (3.1.10)
 (e:/texlive/2024/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshapes.misc.code.tex
File: pgflibraryshapes.misc.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (e:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibraryshapes.symbols.code.tex
File: tikzlibraryshapes.symbols.code.tex 2023-01-15 v3.1.10 (3.1.10)
 (e:/texlive/2024/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshapes.symbols.code.tex
File: pgflibraryshapes.symbols.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (e:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibraryshapes.arrows.code.tex
File: tikzlibraryshapes.arrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
 (e:/texlive/2024/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshapes.arrows.code.tex
File: pgflibraryshapes.arrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (e:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibraryshapes.callouts.code.tex (e:/texlive/2024/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshapes.callouts.code.tex)) (e:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibraryshapes.multipart.code.tex
File: tikzlibraryshapes.multipart.code.tex 2023-01-15 v3.1.10 (3.1.10)
 (e:/texlive/2024/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshapes.multipart.code.tex
File: pgflibraryshapes.multipart.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodepartlowerbox=\box64
\pgfnodeparttwobox=\box65
\pgfnodepartthreebox=\box66
\pgfnodepartfourbox=\box67
\pgfnodeparttwentybox=\box68
\pgfnodepartnineteenbox=\box69
\pgfnodeparteighteenbox=\box70
\pgfnodepartseventeenbox=\box71
\pgfnodepartsixteenbox=\box72
\pgfnodepartfifteenbox=\box73
\pgfnodepartfourteenbox=\box74
\pgfnodepartthirteenbox=\box75
\pgfnodeparttwelvebox=\box76
\pgfnodepartelevenbox=\box77
\pgfnodeparttenbox=\box78
\pgfnodepartninebox=\box79
\pgfnodeparteightbox=\box80
\pgfnodepartsevenbox=\box81
\pgfnodepartsixbox=\box82
\pgfnodepartfivebox=\box83
))) (e:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibraryarrows.code.tex
File: tikzlibraryarrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
 (e:/texlive/2024/texmf-dist/tex/generic/pgf/libraries/pgflibraryarrows.code.tex
File: pgflibraryarrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\arrowsize=\dimen193
)) (e:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarypositioning.code.tex
File: tikzlibrarypositioning.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibraryfit.code.tex
File: tikzlibraryfit.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarybackgrounds.code.tex
File: tikzlibrarybackgrounds.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@layerbox@background=\box84
\pgf@layerboxsaved@background=\box85
) (e:/texlive/2024/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-02-20 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count286
\l__pdf_internal_box=\box86
) (./DWA_RL_Framework_Flowchart.aux)
\openout1 = `DWA_RL_Framework_Flowchart.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for C00/song/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for C05/song/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for C09/song/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for C10/song/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for C20/song/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for C19/song/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for C40/song/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for C42/song/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for C43/song/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for C50/song/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for C52/song/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for C49/song/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for C60/mj/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for C61/mj/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for C63/mj/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for C64/mj/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for C65/mj/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for C70/song/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for C31/song/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for C32/song/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for C33/song/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for C34/song/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for C35/song/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for C36/song/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for C37/song/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for C80/song/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for C81/song/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for C01/song/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for C11/song/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for C21/song/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for C41/song/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
LaTeX Font Info:    Checking defaults for C62/song/m/n on input line 8.
LaTeX Font Info:    ... okay on input line 8.
 (e:/texlive/2024/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count287
\scratchdimen=\dimen194
\scratchbox=\box87
\nofMPsegments=\count288
\nofMParguments=\count289
\everyMPshowfont=\toks33
\MPscratchCnt=\count290
\MPscratchDim=\dimen195
\MPnumerator=\count291
\makeMPintoPDFobject=\count292
\everyMPtoPDFconversion=\toks34
) (e:/texlive/2024/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (e:/texlive/2024/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
)) (e:/texlive/2024/texmf-dist/tex/latex/cjk/texinput/UTF8/UTF8.bdg
File: UTF8.bdg 2021/10/16 4.8.5
) (e:/texlive/2024/texmf-dist/tex/latex/cjk/texinput/UTF8/UTF8.enc
File: UTF8.enc 2021/10/16 4.8.5
) (e:/texlive/2024/texmf-dist/tex/latex/cjk/texinput/UTF8/UTF8.chr
File: UTF8.chr 2021/10/16 4.8.5
)
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <7> on input line 27.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <5> on input line 27.
 [1

{e:/texlive/2024/texmf-var/fonts/map/pdftex/updmap/pdftex.map}{e:/texlive/2024/texmf-dist/fonts/enc/dvips/cm-super/cm-super-t1.enc}] (./DWA_RL_Framework_Flowchart.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
 ***********
 ) 
Here is how much of TeX's memory you used:
 16425 strings out of 474116
 350340 string characters out of 5747716
 1953190 words of memory out of 5000000
 38500 multiletter control sequences out of 15000+600000
 559759 words of font info for 40 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 117i,8n,121p,464b,1449s stack positions out of 10000i,1000n,20000p,200000b,200000s
<e:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx10.pfb><e:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi10.pfb><e:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi7.pfb><e:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr10.pfb><e:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr7.pfb><e:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy10.pfb><e:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy7.pfb><e:/texlive/2024/texmf-dist/fonts/type1/public/cm-super/sfrm1000.pfb>
Output written on DWA_RL_Framework_Flowchart.pdf (1 page, 99069 bytes).
PDF statistics:
 52 PDF objects out of 1000 (max. 8388607)
 32 compressed objects within 1 object stream
 0 named destinations out of 1000 (max. 500000)
 13 words of extra memory for PDF output out of 10000 (max. 10000000)

