# DWA-RL Framework

TD3-DWA-RL分层安全控制架构，支持简单到复杂到动态环境的渐进式训练。

## 核心文件

### 训练测试框架
- `train_dwa_rl.py` - 主训练脚本
- `test_dwa_rl.py` - 主测试脚本  
- `test_dwa_rl_static.py` - 静态测试脚本
- `staged_training.py` - 分阶段训练
- `test_complex_environment.py` - 复杂环境测试

### 核心模块
- `dwa_rl_core.py` - 核心控制器
- `td3_dwa_rl_architecture.py` - TD3架构
- `simple_environment.py` - 环境定义
- `environment_config.py` - 环境配置

### 可视化
- `generate_animated_gif.py` - GIF生成器
- `fixed_gif_generator.py` - 修复版GIF生成器

## 使用方法

### 基础训练
```bash
# 简单环境
python train_dwa_rl.py --episodes 50

# 复杂环境
python train_dwa_rl.py --episodes 100 --env-config complex_static
```

### 分阶段训练（推荐）
```bash
python staged_training.py
```

### 测试
```bash
# 动态测试
python test_dwa_rl.py --episodes 3

# 静态测试
python test_dwa_rl_static.py --episodes 2
```

### 生成GIF
```bash
python fixed_gif_generator.py --model training_outputs/dwa_rl_model_*.pth --steps 300 --fps 10
```

## 环境配置

- `simple`: 3-5个静态障碍物
- `complex_static`: 15-20个静态障碍物  
- `complex_dynamic`: 15-20个静态 + 2-4个动态障碍物
- `extreme`: 20-25个静态 + 4-6个动态障碍物

## 输出文件

### 训练结果 (`training_outputs/`)
- `dwa_rl_model_*.pth` - 训练模型
- `training_report_*.json` - 训练报告
- `training_rewards_*.csv` - 奖励数据
- `training_analysis_*.png` - 分析图表

### 测试结果
- `*.gif` - 导航动画
- 测试图片和数据文件
